import sqlite3
import re

def extract_product_images(text, db_path):
    # 1. <PERSON><PERSON><PERSON>ố<PERSON> database
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()

    # 2. <PERSON><PERSON><PERSON> tất cả tên sản phẩm
    cursor.execute("SELECT DISTINCT Name_Product FROM Product")
    all_product_names = [row[0] for row in cursor.fetchall()]

    cleaned_text = text.lower()

    # 3. Tìm sản phẩm match và vị trí xuất hiện đầu tiên
    matched_products_with_position = []
    seen_products = set()  # Để tránh trùng lặp

    for name in all_product_names:
        lowered_name = name.lower()
        # Tối ưu regex: chỉ cần escape tên, không cần \b chặn
        pattern = re.escape(lowered_name)
        if re.search(pattern, cleaned_text) and name not in seen_products:
            position = cleaned_text.find(lowered_name)
            matched_products_with_position.append((name, position))
            seen_products.add(name)

    # 4. <PERSON>ắ<PERSON> xếp theo thứ tự xuất hiện trong text
    matched_products_with_position.sort(key=lambda x: x[1])
    matched_products = [name for name, _ in matched_products_with_position]

    print(f"Matched products (no duplicates): {matched_products}")

    # 5. Truy vấn ảnh cho từng sản phẩm
    result = []
    for product_name in matched_products:
        cursor.execute("""
            SELECT Link_Image
            FROM Variant v
            JOIN Product p ON v.Product_id = p.Id
            WHERE p.Name_Product = ?
            LIMIT 1
        """, (product_name,))
        row = cursor.fetchone()
        if row:
            result.append({
                "name": product_name,
                "image": row[0]
            })

    conn.close()
    print(f"Final result with {len(result)} unique products")
    return result
